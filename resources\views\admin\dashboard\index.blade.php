@extends('admin.layouts.admin')
@section('page_title', 'Dashboard')

@section('content')
    <div class="row g-3">
        <div class="col-md-3">
            <div class="card card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-muted small">Sản phẩm</div>
                        <div class="fs-4 fw-bold">{{ $metrics['products'] ?? 0 }}</div>
                    </div>
                    <i class="bi bi-box fs-2 text-primary"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-muted small">Bài viết</div>
                        <div class="fs-4 fw-bold">{{ $metrics['posts'] ?? 0 }}</div>
                    </div>
                    <i class="bi bi-newspaper fs-2 text-success"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-muted small">Leads (7 ngày)</div>
                        <div class="fs-4 fw-bold">{{ $metrics['leads_week'] ?? 0 }}</div>
                    </div>
                    <i class="bi bi-inbox fs-2 text-warning"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-muted small">Đối tác</div>
                        <div class="fs-4 fw-bold">{{ $metrics['partners'] ?? 0 }}</div>
                    </div>
                    <i class="bi bi-people fs-2 text-info"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="card mt-3">
        <div class="card-header">Leads mới nhất</div>
        <div class="table-responsive">
            <table class="table table-sm table-hover align-middle mb-0">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Tên</th>
                        <th>Điện thoại</th>
                        <th>Nguồn</th>
                        <th>Thời gian</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse(($latestLeads ?? []) as $lead)
                        <tr>
                            <td>{{ $lead->id }}</td>
                            <td>{{ $lead->name }}</td>
                            <td>{{ $lead->phone }}</td>
                            <td>{{ $lead->source }}</td>
                            <td>{{ $lead->created_at->format('d/m/Y H:i') }}</td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="text-muted">Chưa có lead.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
@endsection
