<?php
namespace App\Models;

use App\Enums\MetaRobots;
use App\Enums\Rel;
use App\Enums\Target;
use Illuminate\Database\Eloquent\Model;

class ProductCategory extends Model
{
    protected $table = 'product_categories';

    protected $primaryKey = 'id';

    protected $fillable = [
        'id',
        'name',
        'slug',
        'summary',
        'description',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'meta_robots',
        'rel',
        'target',
        'status',
        'published_at',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'meta_robots'  => MetaRobots::class,
        'rel'          => Rel::class,
        'target'       => Target::class,
        'status'       => 'boolean',
        'published_at' => 'datetime',
    ];

    public function products()
    {
        return $this->hasMany(Product::class);
    }
}
