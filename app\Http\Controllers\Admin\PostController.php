<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

class PostController extends Controller
{
    public function index()
    {
        return view('admin.posts.index');
    }

    public function create()
    {
        return view('admin.posts.create');
    }

    public function store()
    {
        return redirect()->route('admin.posts.index');
    }

    public function show($id)
    {
        return view('admin.posts.show', compact('id'));
    }

    public function edit($id)
    {}

    public function update($id)
    {}

    public function destroy($id)
    {}
}
