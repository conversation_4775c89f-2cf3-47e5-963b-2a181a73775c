<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models;

class DashboardController extends Controller
{
    /**
     * Trang dashboard
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     */

    public function index()
    {
        $metrics = [
            'products'   => Models\Product::count(),
            'posts'      => Models\Post::count(),
            'partners'   => Models\Partner::count(),
            'leads_week' => Models\Lead::where('created_at', '>=', now()->subDays(7))->count(),
        ];

        $latestLeads = Models\Lead::latest()->limit(10)->get();

        return view('admin.dashboard.index', compact('metrics', 'latestLeads'));
    }
}
