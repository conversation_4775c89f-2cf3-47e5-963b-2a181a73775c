<?php
namespace App\Http\Requests\Admin;

use App\Models\ProductCategory;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
// nhớ import

class ValidateProductCategoryRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
                                                  // LẤY ĐÚNG THAM SỐ ROUTE
                                                  // Với resource 'product_categories', tham số mặc định là 'product_category' (số ít)
        $param = $this->route('product_category') // model hoặc ID

        // phòng khi bạn custom parameters
        $categoryId = $param instanceof ProductCategory ? $param->getKey() : $param;

        // fallback
        $slugRule = Rule::unique('product_categories', 'slug');
        if (! empty($categoryId)) {
            $slugRule = $slugRule->ignore($categoryId); // Convert về ID chắc chắn
        }

        return [
            'name'             => ['required', 'string', 'max:255'],
            'slug'             => ['required', 'string', 'max:255', $slugRule],
            'summary'          => ['nullable', 'string'],
            'description'      => ['nullable', 'string'],
            'meta_title'       => ['nullable', 'string', 'max:1000'],
            'meta_description' => ['nullable', 'string', 'max:1000'],
            'meta_keywords'    => ['nullable', 'string', 'max:1000'],
            'meta_robots'      => ['required', Rule::in(['index, follow', 'noindex, nofollow', 'index, nofollow', 'noindex, follow'])],
            'rel'              => ['required', Rule::in(['dofollow', 'nofollow'])],
            'target'           => ['required', Rule::in(['_self', '_blank'])],
            'status'           => ['sometimes', 'boolean'],
            'published_at'     => ['nullable', 'date'],
        ];
    }

    protected function prepareForValidation(): void
    {
        // Xây rule unique linh hoạt (store vs update)
        $slug = $this->filled('slug') ? $this->input('slug') : $this->input('name');
        if ($slug) {
            $this->merge(['slug' => \Str::slug($slug)]);
        }
        $this->merge(['status' => $this->boolean('status')]);
    }

    public function messages(): array
    {
        return [
            'slug.unique'    => 'Slug đã tồn tại, vui lòng chọn slug khác.',
            'meta_robots.in' => 'Giá trị meta_robots không hợp lệ.',
            'rel.in'         => 'Giá trị rel không hợp lệ.',
            'target.in'      => 'Giá trị target không hợp lệ.',
        ];
    }
}
