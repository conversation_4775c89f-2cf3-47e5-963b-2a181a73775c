<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('image_path')->nullable();
            $table->foreignId('product_category_id')->nullable()->constrained()->nullOnDelete();
            $table->text('excerpt')->nullable();
            $table->longText('content')->nullable();
            $table->decimal('interest_min', 5, 2)->nullable();
            $table->decimal('interest_max', 5, 2)->nullable();
            $table->bigInteger('limit_max')->nullable();
            $table->text('summary')->nullable()->collation('utf8mb4_general_ci');
            $table->longText('description')->nullable()->collation('utf8mb4_general_ci');
            $table->string('meta_title', 1000)->nullable()->collation('utf8mb4_general_ci');
            $table->string('meta_description', 1000)->nullable()->collation('utf8mb4_general_ci');
            $table->string('meta_keywords', 1000)->nullable()->collation('utf8mb4_general_ci');

            $table->enum('meta_robots', [
                'index, follow',
                'noindex, nofollow',
                'index, nofollow',
                'noindex, follow',
            ])->default('index, follow');
            $table->enum('rel', [
                'dofollow',
                'nofollow',
            ])->default('dofollow');
            $table->enum('target', [
                '_self',
                '_blank',
            ])->default('_self');
            $table->integer('view')->default(0);
            $table->boolean('status')->default(false);
            $table->timestamp('published_at')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
