@extends('admin.layouts.admin')
@section('page_title', 'S<PERSON>a sản phẩm')

@section('content')
    <div class="card">
        <div class="card-header fw-semibold"><PERSON><PERSON><PERSON> sản phẩm</div>
        <div class="card-body">
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $e)
                            <li>{{ $e }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            <form method="POST" action="{{ route('admin.products.update', $product->id) }}" enctype="multipart/form-data">
                @method('PUT')
                @include('admin.products._form', ['product' => $product])
            </form>
        </div>
    </div>
@endsection
