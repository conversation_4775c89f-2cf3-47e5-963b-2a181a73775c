<?php
namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ValidateProductRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $product = $this->route('product');

        return [
            'name'                => ['required', 'string', 'max:255'],
            'slug'                => [
                'required', 'string', 'max:255',
                Rule::unique('products', 'slug')->ignore(optional($product)->id),
            ],
            'image'               => ['nullable', 'image', 'max:2048'],
            'product_category_id' => ['nullable', 'exists:product_categories,id'],
            'excerpt'             => ['nullable', 'string'],
            'content'             => ['nullable', 'string'],
            'interest_min'        => ['nullable', 'numeric', 'min:0'],
            'interest_max'        => ['nullable', 'numeric', 'min:0', 'gte:interest_min'],
            'limit_max'           => ['nullable', 'integer', 'min:0'],
            'summary'             => ['nullable', 'string'],
            'description'         => ['nullable', 'string'],
            'meta_title'          => ['nullable', 'string', 'max:1000'],
            'meta_description'    => ['nullable', 'string', 'max:1000'],
            'meta_keywords'       => ['nullable', 'string', 'max:1000'],
            'meta_robots'         => ['required', Rule::in(['index, follow', 'noindex, nofollow', 'index, nofollow', 'noindex, follow'])],
            'rel'                 => ['required', Rule::in(['dofollow', 'nofollow'])],
            'target'              => ['required', Rule::in(['_self', '_blank'])],
            'view'                => ['nullable', 'integer', 'min:0'],
            'status'              => ['sometimes', 'boolean'],
            'published_at'        => ['nullable', 'date'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'status' => $this->boolean('status'),
        ]);
    }

    public function messages(): array
    {
        return [
            'slug.unique'      => 'Slug đã tồn tại, vui lòng chọn slug khác.',
            'interest_max.gte' => 'Lãi suất tối đa phải ≥ lãi suất tối thiểu.',
        ];
    }
}
