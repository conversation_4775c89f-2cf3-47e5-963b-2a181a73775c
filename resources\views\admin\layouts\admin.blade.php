@php($title = $title ?? 'Admin')
<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $title }} — Admin</title>

    {{-- Bootstrap & Icons --}}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">

    {{-- Admin CSS riêng --}}
    <link rel="stylesheet" href="{{ asset('admin/css/admin.css') }}">
    @stack('head')
</head>

<body class="bg-light">

    <div class="d-flex">
        @include('admin.partials.sidebar')

        <div class="flex-grow-1 min-vh-100">
            @include('admin.partials.topbar')

            <main class="container-fluid p-4">
                @yield('content')
            </main>
        </div>
    </div>

    {{-- JS --}}
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ asset('admin/js/admin.js') }}"></script>
    @stack('scripts')
</body>

</html>
