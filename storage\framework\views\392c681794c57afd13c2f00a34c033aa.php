
<?php $__env->startSection('page_title', 'Sửa danh mục'); ?>

<?php $__env->startSection('content'); ?>
    <div class="card">
        <div class="card-header fw-semibold">S<PERSON>a danh mục</div>
        <div class="card-body">
            <?php if($errors->any()): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($error); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            <?php endif; ?>

            <form method="POST" action="<?php echo e(route('admin.product_categories.update', $cat->id)); ?>">
                <?php echo method_field('PUT'); ?>
                <?php echo $__env->make('admin.product_categories._form', ['cat' => $cat], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\example\resources\views/admin/product_categories/edit.blade.php ENDPATH**/ ?>