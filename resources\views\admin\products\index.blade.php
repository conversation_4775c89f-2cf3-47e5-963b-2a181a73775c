@extends('admin.layouts.admin')
@section('page_title', 'Sản phẩm')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h4 m-0"><PERSON><PERSON><PERSON> phẩm</h1>
        <a href="{{ route('admin.products.create') }}" class="btn btn-primary">
            <i class="bi bi-plus"></i> Thêm sản phẩm
        </a>
    </div>

    @if (session('success'))
        <div class="alert alert-success">{{ session('success') }}</div>
    @endif

    <form method="GET" class="row g-2 mb-3">
        <div class="col-md-4">
            <input type="text" name="q" value="{{ $q }}" class="form-control" placeholder="Tìm theo tên">
        </div>
        <div class="col-md-4">
            <select name="category" class="form-select">
                <option value="">Tất cả danh mục</option>
                @foreach ($categories as $c)
                    <option value="{{ $c->id }}" @selected($cat == $c->id)>{{ $c->name }}</option>
                @endforeach
            </select>
        </div>
        <div class="col-md-4 d-grid d-md-block">
            <button class="btn btn-outline-primary"><i class="bi bi-funnel"></i> Lọc</button>
            <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">Reset</a>
        </div>
    </form>

    <div class="card">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>Ảnh</th>
                        <th>Tên</th>
                        <th>Danh mục</th>
                        <th>Lãi suất</th>
                        <th>Hạn mức</th>
                        <th>Hiển thị</th>
                        <th class="text-end">Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($products as $p)
                        <tr>
                            <td>{{ $p->id }}</td>
                            <td style="width:60px">
                                @if ($p->image_path)
                                    <img src="{{ asset('storage/' . $p->image_path) }}" class="img-thumbnail"
                                        style="width:56px;height:56px;object-fit:cover">
                                @endif
                            </td>
                            <td>
                                <a class="text-decoration-none"
                                    href="{{ route('admin.products.show', $p->id) }}">{{ $p->name }}</a>
                                <div class="text-muted small"><code>{{ $p->slug }}</code></div>
                            </td>
                            <td>{{ $p->category->name ?? '—' }}</td>
                            <td>{{ $p->interest_min }}–{{ $p->interest_max }}%</td>
                            <td>{{ number_format($p->limit_max) }}</td>
                            <td>
                                @if ($p->status)
                                    <span class="badge bg-success">Hiển thị</span>
                                @else
                                    <span class="badge bg-danger">Ẩn</span>
                                @endif
                            </td>
                            <td class="text-end">
                                <a href="{{ route('admin.products.edit', $p->id) }}"
                                    class="btn btn-sm btn-outline-secondary">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <form action="{{ route('admin.products.destroy', $p->id) }}" method="POST"
                                    class="d-inline" onsubmit="return confirm('Xóa sản phẩm này?')">
                                    @csrf @method('DELETE')
                                    <button class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                </form>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="8" class="text-center text-muted py-4">Chưa có sản phẩm</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="card-footer">
            {{ $products->links() }}
        </div>
    </div>
@endsection
