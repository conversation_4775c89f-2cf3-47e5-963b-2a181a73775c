@csrf
<div class="row g-3">
    <div class="col-md-6">
        <label class="form-label">T<PERSON><PERSON> sản phẩm *</label>
        <input type="text" name="name" id="name" class="form-control"
            value="{{ old('name', $product->name ?? '') }}" required>
    </div>
    <div class="col-md-6">
        <label class="form-label">Slug *</label>
        <input type="text" name="slug" id="slug" class="form-control"
            value="{{ old('slug', $product->slug ?? '') }}" required>
    </div>

    <div class="col-md-6">
        <label class="form-label"><PERSON>h mục</label>
        <select name="product_category_id" class="form-select">
            <option value="">— Ch<PERSON>n danh mục —</option>
            @foreach ($categories as $c)
                <option value="{{ $c->id }}" @selected(old('product_category_id', $product->product_category_id ?? '') == $c->id)>{{ $c->name }}</option>
            @endforeach
        </select>
    </div>

    <div class="col-md-6">
        <label class="form-label">Ảnh (tùy chọn)</label>
        <input type="file" name="image" class="form-control">
        @if (!empty($product?->image_path))
            <div class="form-text">Ảnh hiện tại: <a target="_blank"
                    href="{{ asset('storage/' . $product->image_path) }}">{{ $product->image_path }}</a></div>
        @endif
    </div>

    <div class="col-12">
        <label class="form-label">Tóm tắt (excerpt)</label>
        <textarea name="excerpt" rows="2" class="form-control">{{ old('excerpt', $product->excerpt ?? '') }}</textarea>
    </div>

    <div class="col-12">
        <label class="form-label">Nội dung (content)</label>
        <textarea name="content" rows="6" class="form-control">{{ old('content', $product->content ?? '') }}</textarea>
    </div>

    <div class="col-md-4">
        <label class="form-label">Lãi suất (min %)</label>
        <input type="number" step="0.01" name="interest_min" class="form-control"
            value="{{ old('interest_min', $product->interest_min ?? '') }}">
    </div>
    <div class="col-md-4">
        <label class="form-label">Lãi suất (max %)</label>
        <input type="number" step="0.01" name="interest_max" class="form-control"
            value="{{ old('interest_max', $product->interest_max ?? '') }}">
    </div>
    <div class="col-md-4">
        <label class="form-label">Hạn mức tối đa</label>
        <input type="number" name="limit_max" class="form-control"
            value="{{ old('limit_max', $product->limit_max ?? '') }}">
    </div>

    <div class="col-12">
        <label class="form-label">Summary</label>
        <textarea name="summary" rows="2" class="form-control">{{ old('summary', $product->summary ?? '') }}</textarea>
    </div>

    <div class="col-12">
        <label class="form-label">Description</label>
        <textarea name="description" rows="5" class="form-control">{{ old('description', $product->description ?? '') }}</textarea>
    </div>

    <div class="col-md-6">
        <label class="form-label">Meta title</label>
        <input type="text" name="meta_title" class="form-control"
            value="{{ old('meta_title', $product->meta_title ?? '') }}">
    </div>
    <div class="col-md-6">
        <label class="form-label">Meta description</label>
        <input type="text" name="meta_description" class="form-control"
            value="{{ old('meta_description', $product->meta_description ?? '') }}">
    </div>
    <div class="col-12">
        <label class="form-label">Meta keywords</label>
        <input type="text" name="meta_keywords" class="form-control"
            value="{{ old('meta_keywords', $product->meta_keywords ?? '') }}">
    </div>

    <div class="col-md-4">
        <label class="form-label">Meta robots *</label>
        @php $robots = ['index, follow','noindex, nofollow','index, nofollow','noindex, follow']; @endphp
        <select name="meta_robots" class="form-select" required>
            @foreach ($robots as $r)
                <option value="{{ $r }}" @selected(old('meta_robots', $product->meta_robots->value ?? 'index, follow') === $r)>{{ $r }}</option>
            @endforeach
        </select>
    </div>
    <div class="col-md-4">
        <label class="form-label">rel *</label>
        @php $rels = ['dofollow','nofollow']; @endphp
        <select name="rel" class="form-select" required>
            @foreach ($rels as $r)
                <option value="{{ $r }}" @selected(old('rel', $product->rel->value ?? 'dofollow') === $r)>{{ $r }}</option>
            @endforeach
        </select>
    </div>
    <div class="col-md-4">
        <label class="form-label">target *</label>
        @php $targets = ['_self','_blank']; @endphp
        <select name="target" class="form-select" required>
            @foreach ($targets as $t)
                <option value="{{ $t }}" @selected(old('target', $product->target->value ?? '_self') === $t)>{{ $t }}</option>
            @endforeach
        </select>
    </div>

    <div class="col-md-3">
        <label class="form-label">Lượt xem</label>
        <input type="number" name="view" class="form-control" value="{{ old('view', $product->view ?? 0) }}">
    </div>

    <div class="col-md-3">
        <label class="form-label">Trạng thái</label>
        <div class="form-check">
            <input class="form-check-input" type="checkbox" name="status" value="1" id="status"
                @checked(old('status', $product->status ?? false))>
            <label class="form-check-label" for="status">Bật hiển thị</label>
        </div>
    </div>

    <div class="col-md-6">
        <label class="form-label">Ngày publish</label>
        <input type="datetime-local" name="published_at" class="form-control"
            value="{{ old('published_at', isset($product->published_at) ? $product->published_at->format('Y-m-d\TH:i') : '') }}">
    </div>

    <div class="col-12 d-flex gap-2">
        <button class="btn btn-primary"><i class="bi bi-save"></i> Lưu</button>
        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">Hủy</a>
    </div>
</div>

@push('scripts')
    <script>
        (function() {
            const nameInput = document.getElementById('name');
            const slugInput = document.getElementById('slug');
            if (!nameInput || !slugInput) return;

            let userTouchedSlug = !!slugInput.value;
            slugInput.addEventListener('input', () => userTouchedSlug = true);

            nameInput.addEventListener('input', () => {
                if (userTouchedSlug) return;
                const s = nameInput.value.toLowerCase()
                    .normalize('NFD').replace(/[\u0300-\u036f]/g, '')
                    .replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
                slugInput.value = s;
            });
        })();
    </script>
@endpush
