<?php echo csrf_field(); ?>
<div class="row g-3">
    <div class="col-md-6">
        <label class="form-label">Tên danh mục *</label>
        <input type="text" name="name" class="form-control" id="name" value="<?php echo e(old('name', $cat->name ?? '')); ?>"
            required>
    </div>

    <div class="col-md-6">
        <label class="form-label">Slug *</label>
        <input type="text" name="slug" class="form-control" id="slug"
            value="<?php echo e(old('slug', $cat->slug ?? '')); ?>" required>
        <div class="form-text"><PERSON><PERSON> thể tự nhập hoặc sinh từ tên.</div>
    </div>

    <div class="col-12">
        <label class="form-label">Tóm tắt (summary)</label>
        <textarea name="summary" rows="2" class="form-control"><?php echo e(old('summary', $cat->summary ?? '')); ?></textarea>
    </div>

    <div class="col-12">
        <label class="form-label"><PERSON><PERSON> tả (description)</label>
        <textarea name="description" rows="5" class="form-control"><?php echo e(old('description', $cat->description ?? '')); ?></textarea>
    </div>

    <div class="col-md-6">
        <label class="form-label">Meta title</label>
        <input type="text" name="meta_title" class="form-control"
            value="<?php echo e(old('meta_title', $cat->meta_title ?? '')); ?>">
    </div>

    <div class="col-md-6">
        <label class="form-label">Meta description</label>
        <input type="text" name="meta_description" class="form-control"
            value="<?php echo e(old('meta_description', $cat->meta_description ?? '')); ?>">
    </div>

    <div class="col-12">
        <label class="form-label">Meta keywords</label>
        <input type="text" name="meta_keywords" class="form-control"
            value="<?php echo e(old('meta_keywords', $cat->meta_keywords ?? '')); ?>">
    </div>

    <div class="col-md-4">
        <label class="form-label">Meta robots *</label>
        <?php $robots = ['index, follow','noindex, nofollow','index, nofollow','noindex, follow']; ?>
        <select name="meta_robots" class="form-select" required>
            <?php $__currentLoopData = $robots; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $r): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($r); ?>" <?php if(old('meta_robots', $cat->meta_robots->value ?? 'index, follow') === $r): echo 'selected'; endif; ?>><?php echo e($r); ?></option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
    </div>

    <div class="col-md-4">
        <label class="form-label">rel *</label>
        <?php $rels = ['dofollow','nofollow']; ?>
        <select name="rel" class="form-select" required>
            <?php $__currentLoopData = $rels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $r): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($r); ?>" <?php if(old('rel', $cat->rel->value ?? 'dofollow') === $r): echo 'selected'; endif; ?>><?php echo e($r); ?></option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
    </div>

    <div class="col-md-4">
        <label class="form-label">target *</label>
        <?php $targets = ['_self','_blank']; ?>
        <select name="target" class="form-select" required>
            <?php $__currentLoopData = $targets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $t): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($t); ?>" <?php if(old('target', $cat->target->value ?? '_self') === $t): echo 'selected'; endif; ?>><?php echo e($t); ?></option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
    </div>

    <div class="col-md-3">
        <label class="form-label">Trạng thái</label>
        <div class="form-check">
            <input class="form-check-input" type="checkbox" name="status" value="1" id="status"
                <?php if(old('status', $cat->status ?? false)): echo 'checked'; endif; ?>>
            <label class="form-check-label" for="status">Bật hiển thị</label>
        </div>
    </div>

    <div class="col-md-5">
        <label class="form-label">Ngày publish</label>
        <input type="datetime-local" name="published_at" class="form-control"
            value="<?php echo e(old('published_at', isset($cat->published_at) ? $cat->published_at->format('Y-m-d\TH:i') : '')); ?>">
    </div>

    <div class="col-12 d-flex gap-2">
        <button class="btn btn-primary"><i class="bi bi-save"></i> Lưu</button>
        <a href="<?php echo e(route('admin.product_categories.index')); ?>" class="btn btn-outline-secondary">Hủy</a>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
    <script>
        // Tự sinh slug cơ bản từ name (giữ nguyên nếu bạn đang gõ slug thủ công)
        (function() {
            const nameInput = document.getElementById('name');
            const slugInput = document.getElementById('slug');
            if (!nameInput || !slugInput) return;

            let userTouchedSlug = !!slugInput.value;
            slugInput.addEventListener('input', () => userTouchedSlug = true);

            nameInput.addEventListener('input', () => {
                if (userTouchedSlug) return;
                const s = nameInput.value.toLowerCase()
                    .normalize('NFD').replace(/[\u0300-\u036f]/g, '') // bỏ dấu
                    .replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
                slugInput.value = s;
            });
        })();
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\example\resources\views/admin/product_categories/_form.blade.php ENDPATH**/ ?>