<?php

// Controller
use App\Http\Controllers\Admin;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes`
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

/**
 * ADMIN
 */

Route::view('/login', 'admin.auth.login')->name('login');
Route::post('/login', [Admin\AuthController::class, 'login'])->name('login');
Route::get('/register', [Admin\AuthController::class, 'register'])->name('register');

Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [Admin\DashboardController::class, 'index'])->name('dashboard');

    Route::post('/logout', [Admin\AuthController::class, 'logout'])->name('logout');

    Route::resource('products', Admin\ProductController::class)->names('products');
    Route::resource('product_categories', Admin\ProductCategoryController::class)->names('product_categories')
        ->parameters(['product_categories' => 'product_category']);
    ;

    Route::resource('posts', Admin\PostController::class)->names('posts');
    Route::resource('partners', Admin\PartnerController::class)->names('partners');
    Route::resource('leads', Admin\LeadController::class)->only(['index', 'show', 'destroy'])->names('leads');
});
