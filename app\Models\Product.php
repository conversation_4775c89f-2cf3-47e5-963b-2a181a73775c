<?php
namespace App\Models;

use App\Enums\MetaRobots;
use App\Enums\Rel;
use App\Enums\Target;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    protected $fillable = [
        'id',
        'name',
        'slug',
        'image_path',
        'product_category_id',
        'excerpt',
        'content',
        'interest_min',
        'interest_max',
        'limit_max',
        'summary',
        'description',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'meta_robots',
        'rel',
        'target',
        'view',
        'status',
        'published_at',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'meta_robots'  => MetaRobots::class,
        'rel'          => Rel::class,
        'target'       => Target::class,
        'status'       => 'boolean',
        'published_at' => 'datetime',
    ];

    public function category()
    {
        return $this->belongsTo(ProductCategory::class);
    }

    // Scope: chỉ lấy sản phẩm publish
    public function scopePublished($query)
    {
        return $query->where('status', true)->whereNotNull('published_at');
    }
}
