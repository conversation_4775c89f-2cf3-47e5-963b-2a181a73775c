@extends('admin.layouts.admin')
@section('page_title', '<PERSON> tiết sản phẩm')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h4 m-0">Chi tiết: {{ $product->name }}</h1>
        <div class="d-flex gap-2">
            <a class="btn btn-outline-secondary" href="{{ route('admin.products.edit', $product->id) }}">
                <i class="bi bi-pencil"></i> Sửa
            </a>
            <a class="btn btn-outline-dark" href="{{ route('admin.products.index') }}">
                <i class="bi bi-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-4">
                    @if ($product->image_path)
                        <img src="{{ asset('storage/' . $product->image_path) }}" class="img-fluid rounded">
                    @else
                        <div class="text-muted">Ch<PERSON><PERSON> có ảnh</div>
                    @endif
                </div>
                <div class="col-md-8">
                    <dl class="row mb-0">
                        <dt class="col-sm-4">Slug</dt>
                        <dd class="col-sm-8"><code>{{ $product->slug }}</code></dd>
                        <dt class="col-sm-4">Danh mục</dt>
                        <dd class="col-sm-8">{{ $product->category->name ?? '—' }}</dd>
                        <dt class="col-sm-4">Lãi suất</dt>
                        <dd class="col-sm-8">{{ $product->interest_min }}–{{ $product->interest_max }}%</dd>
                        <dt class="col-sm-4">Hạn mức</dt>
                        <dd class="col-sm-8">{{ number_format($product->limit_max) }}</dd>
                        <dt class="col-sm-4">Hiển thị</dt>
                        <dd class="col-sm-8">{{ $product->status ? 'Bật' : 'Tắt' }}</dd>
                        <dt class="col-sm-4">Publish</dt>
                        <dd class="col-sm-8">{{ $product->published_at?->format('d/m/Y H:i') }}</dd>
                        <dt class="col-sm-4">Lượt xem</dt>
                        <dd class="col-sm-8">{{ $product->view }}</dd>
                    </dl>
                </div>
            </div>

            <hr>
            <h6>Excerpt</h6>
            <p class="text-muted">{{ $product->excerpt }}</p>

            <h6>Content</h6>
            <div class="content">{!! nl2br(e($product->content)) !!}</div>

            <hr>
            <h6>SEO</h6>
            <dl class="row mb-0">
                <dt class="col-sm-3">Meta title</dt>
                <dd class="col-sm-9">{{ $product->meta_title }}</dd>
                <dt class="col-sm-3">Meta description</dt>
                <dd class="col-sm-9">{{ $product->meta_description }}</dd>
                <dt class="col-sm-3">Meta keywords</dt>
                <dd class="col-sm-9">{{ $product->meta_keywords }}</dd>
                <dt class="col-sm-3">Meta robots</dt>
                <dd class="col-sm-9">{{ $product->meta_robots->value ?? $product->meta_robots }}</dd>
                <dt class="col-sm-3">rel</dt>
                <dd class="col-sm-9">{{ $product->rel->value ?? $product->rel }}</dd>
                <dt class="col-sm-3">target</dt>
                <dd class="col-sm-9">{{ $product->target->value ?? $product->target }}</dd>
            </dl>
        </div>
    </div>
@endsection
