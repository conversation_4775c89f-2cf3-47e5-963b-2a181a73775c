<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ValidateProductCategoryRequest;
use App\Models\ProductCategory;

class ProductCategoryController extends Controller
{

    /**
     * Summary of index
     * @return never
     */
    public function index()
    {
        $categories = ProductCategory::latest()->paginate(10);

        return view('admin.product_categories.index', compact('categories'));
    }

    public function create()
    {
        return view('admin.product_categories.create');
    }

    public function store(ValidateProductCategoryRequest $request)
    {
        // validate
        $data = $request->validated();

        ProductCategory::create($data);

        return redirect()
            ->route('admin.product_categories.index')
            ->with('success', 'Tạo danh mục thành công!');
    }

    public function show($id)
    {
        $cat = ProductCategory::findOrFail($id);
        return view('admin.product_categories.show', compact('cat'));
    }

    public function edit($id)
    {
        $cat = ProductCategory::findOrFail($id);
        return view('admin.product_categories.edit', compact('cat'));
    }

    public function update(ValidateProductCategoryRequest $request, ProductCategory $cat)
    {

        // validate
        $cat->update($request->validated());

        return redirect()
            ->route('admin.product_categories.index')
            ->with('success', 'Cập nhật danh mục thành công!');
    }

    public function destroy($id)
    {
        $cat = ProductCategory::findOrFail($id);
        $cat->delete();

        return redirect()
            ->route('admin.product_categories.index')
            ->with('success', 'Đã xóa danh mục!');
    }
}
