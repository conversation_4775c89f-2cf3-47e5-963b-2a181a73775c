
<?php $__env->startSection('page_title', '<PERSON>h mục sản phẩm'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h4 m-0"><PERSON><PERSON> mục sản phẩm</h1>
        <a href="<?php echo e(route('admin.product_categories.create')); ?>" class="btn btn-primary">
            <i class="bi bi-plus"></i> Thêm danh mục
        </a>
    </div>

    <?php if(session('success')): ?>
        <div class="alert alert-success"><?php echo e(session('success')); ?></div>
    <?php endif; ?>

    <div class="card">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>Tên</th>
                        <th>Slug</th>
                        <th>Tr<PERSON>ng thái</th>
                        <th>Ngày publish</th>
                        <th class="text-end">Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td><?php echo e($cat->id); ?></td>
                            <td><?php echo e($cat->name); ?></td>
                            <td><span class="badge bg-secondary"><?php echo e($cat->slug); ?></span></td>
                            <td>
                                <?php if($cat->status): ?>
                                    <span class="badge bg-success">Hiển thị</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Ẩn</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($cat->published_at?->format('d/m/Y H:i')); ?></td>
                            <td class="text-end">
                                <a href="<?php echo e(route('admin.product_categories.edit', $cat->id)); ?>"
                                    class="btn btn-sm btn-outline-secondary">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <form action="<?php echo e(route('admin.product_categories.destroy', $cat->id)); ?>" method="POST"
                                    class="d-inline" onsubmit="return confirm('Xóa danh mục này?')">
                                    <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                    <button class="btn btn-sm btn-outline-danger">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="text-center text-muted py-4">Chưa có danh mục nào</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        <div class="card-footer">
            <?php echo e($categories->links()); ?>

        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\example\resources\views/admin/product_categories/index.blade.php ENDPATH**/ ?>