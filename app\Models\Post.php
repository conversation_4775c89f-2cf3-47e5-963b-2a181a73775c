<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Post extends Model
{
    use HasFactory;

    protected $table = 'posts';

    protected $primaryKey = 'id';

    protected $fillable = [
        'id',
        'post_category_id',
        'name',
        'slug',
        'image_path',
        'summary',
        'description',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'meta_robots',
        'rel',
        'target',
        'view',
        'status',
        'published_at',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'meta_robots'  => MetaRobots::class,
        'rel'          => Rel::class,
        'target'       => Target::class,
        'status'       => 'boolean',
        'published_at' => 'datetime',
    ];
}
